@import "tailwindcss";

@font-face {
  font-family: "Lexend";
  src: url("./assets/Lexend-VariableFont.ttf") format("truetype");
  font-weight: 100 900;
  font-style: normal;
  font-display: swap;
}

@theme {
  --font-lexend: "Lexend", sans-serif;
}

/* Mobile font size overrides - smaller fonts for mobile screens */
@media (max-width: 640px) {
  /* Large headings - reduce from text-3xl (30px) to text-2xl (24px) */
  .text-3xl {
    font-size: 1.5rem !important; /* 24px */
    line-height: 2rem !important;
  }

  /* Medium-large headings - reduce from text-2xl (24px) to text-xl (20px) */
  .text-2xl {
    font-size: 1.25rem !important; /* 20px */
    line-height: 1.75rem !important;
  }

  /* Medium headings - reduce from text-xl (20px) to text-lg (18px) */
  .text-xl {
    font-size: 1.125rem !important; /* 18px */
    line-height: 1.75rem !important;
  }

  /* Small headings - reduce from text-lg (18px) to text-base (16px) */
  .text-lg {
    font-size: 1rem !important; /* 16px */
    line-height: 1.5rem !important;
  }

  /* Body text - reduce from text-base (16px) to text-sm (14px) */
  .text-base {
    font-size: 0.875rem !important; /* 14px */
    line-height: 1.25rem !important;
  }

  /* Small text - reduce from text-sm (14px) to text-xs (12px) */
  .text-sm {
    font-size: 0.75rem !important; /* 12px */
    line-height: 1rem !important;
  }
}

button {
  cursor: pointer;
}
